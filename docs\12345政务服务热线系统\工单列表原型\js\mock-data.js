/**
 * 模拟数据生成器
 */

const MockData = {
    // 基础数据
    departments: [
        '市城管局', '市住建局', '市交通局', '市环保局', '市水务局',
        '市教育局', '市卫健委', '市民政局', '市人社局', '市市场监管局',
        '区城管局', '区住建局', '区交通局', '区环保局', '区水务局',
        '街道办事处', '社区居委会', '物业公司', '供水公司', '供电公司'
    ],
    
    areas: [
        '市辖区/中心区/建设街道/阳光社区',
        '市辖区/中心区/民主街道/和谐社区',
        '市辖区/东城区/东风街道/幸福社区',
        '市辖区/东城区/朝阳街道/新城社区',
        '市辖区/西城区/西关街道/老城社区',
        '市辖区/西城区/工业街道/工人社区',
        '市辖区/南区/南门街道/商贸社区',
        '市辖区/南区/开发街道/科技社区',
        '市辖区/北区/北站街道/铁路社区',
        '市辖区/北区/新区街道/现代社区'
    ],
    
    citizenNames: [
        '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
        '陈明', '刘华', '杨丽', '黄强', '朱敏', '林峰', '郭静', '何勇',
        '马超', '许娟', '邓伟', '曾芳', '彭涛', '范琳', '董军', '梁雪'
    ],
    
    phoneNumbers: [
        '138****1234', '139****5678', '150****9012', '151****3456',
        '186****7890', '187****2345', '188****6789', '189****0123',
        '130****4567', '131****8901', '132****2345', '133****6789'
    ],
    
    titles: [
        '小区噪音扰民问题', '道路积水严重', '垃圾清运不及时', '路灯损坏需维修',
        '违章建筑举报', '物业服务质量差', '公交站台破损', '绿化带缺失',
        '停车位不足', '电梯故障频发', '供水压力不足', '网络信号差',
        '学校周边交通拥堵', '医院排队时间长', '政务服务效率低', '环境污染严重',
        '食品安全问题', '价格违规收费', '服务态度恶劣', '设施维护不当'
    ],
    
    // 生成随机工单编号
    generateTicketNumber() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
        return `WD${year}${month}${day}${random}`;
    },
    
    // 生成随机时间
    generateRandomTime(daysAgo = 30) {
        const now = Date.now();
        const randomDays = Math.floor(Math.random() * daysAgo);
        const randomHours = Math.floor(Math.random() * 24);
        const randomMinutes = Math.floor(Math.random() * 60);
        
        return now - (randomDays * 24 * 60 * 60 * 1000) - (randomHours * 60 * 60 * 1000) - (randomMinutes * 60 * 1000);
    },
    
    // 生成时限
    generateTimeLimit(urgency, createTime) {
        const limits = {
            normal: 72 * 60 * 60 * 1000,    // 72小时
            urgent: 24 * 60 * 60 * 1000,    // 24小时
            critical: 4 * 60 * 60 * 1000    // 4小时
        };
        
        return createTime + limits[urgency];
    },
    
    // 随机选择数组元素
    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    },
    
    // 生成单个工单数据
    generateTicket(id) {
        const urgency = this.randomChoice(['normal', 'normal', 'normal', 'urgent', 'critical']);
        const status = this.randomChoice(['draft', 'pending', 'processing', 'review', 'callback', 'closed']);
        const mode = this.randomChoice(['instant', 'normal', 'collaborative', 'supervise']);
        const createTime = this.generateRandomTime();
        const timeLimit = this.generateTimeLimit(urgency, createTime);
        
        // 根据状态生成相应的时间
        let dispatchTime = null;
        let acceptTime = null;
        let finishTime = null;
        
        if (['pending', 'processing', 'review', 'callback', 'closed'].includes(status)) {
            dispatchTime = createTime + Math.floor(Math.random() * 2 * 60 * 60 * 1000); // 2小时内派单
        }
        
        if (['processing', 'review', 'callback', 'closed'].includes(status)) {
            acceptTime = dispatchTime + Math.floor(Math.random() * 4 * 60 * 60 * 1000); // 4小时内接单
        }
        
        if (['closed'].includes(status)) {
            finishTime = acceptTime + Math.floor(Math.random() * 48 * 60 * 60 * 1000); // 48小时内办结
        }
        
        // 生成特殊标识
        const specialTags = [];
        if (Math.random() < 0.1) specialTags.push('vip');
        if (Math.random() < 0.15) specialTags.push('repeat');
        if (Math.random() < 0.05) specialTags.push('media');
        if (Math.random() < 0.03) specialTags.push('leader');
        
        // 生成关联信息
        const hasRelation = Math.random() < 0.1;
        let relationType = null;
        let relationCount = 0;
        let parentTicketId = null;
        
        if (hasRelation) {
            const relations = ['merged', 'split', 'parent', 'child'];
            relationType = this.randomChoice(relations);
            if (relationType === 'merged' || relationType === 'split') {
                relationCount = Math.floor(Math.random() * 5) + 2;
            }
            if (relationType === 'child') {
                parentTicketId = this.generateTicketNumber();
            }
        }
        
        // 生成协办信息
        const isCollaborative = mode === 'collaborative';
        let collaborativeInfo = null;
        let mainDepartment = null;
        let assistDepartments = null;

        if (isCollaborative) {
            mainDepartment = this.randomChoice(this.departments);
            const assistDeptList = [
                this.randomChoice(this.departments),
                this.randomChoice(this.departments)
            ].filter((dept, index, arr) => arr.indexOf(dept) === index && dept !== mainDepartment);

            assistDepartments = assistDeptList;

            collaborativeInfo = {
                mainDepartment: mainDepartment,
                assistDepartments: assistDepartments,
                progress: Math.floor(Math.random() * 100),
                coordinationStatus: this.randomChoice(['coordinating', 'waiting', 'completed'])
            };
        }
        
        return {
            id: id,
            ticketNumber: this.generateTicketNumber(),
            status: status,
            mode: mode,
            urgency: urgency,
            type: this.randomChoice(['complaint', 'suggestion', 'consultation', 'help', 'praise', 'report']),
            title: this.randomChoice(this.titles),
            description: `这是一个关于"${this.randomChoice(this.titles)}"的详细描述。市民反映的问题需要相关部门及时处理和解决。`,
            
            // 市民信息
            citizen: {
                name: this.randomChoice(this.citizenNames),
                phone: this.randomChoice(this.phoneNumbers),
                type: this.randomChoice(['individual', 'enterprise', 'organization']),
                tags: specialTags
            },

            // VIP和客户信息
            vipLevel: specialTags.includes('vip') ? this.randomChoice(['人大代表', '政协委员', '媒体记者', '企业高管']) : '无',
            customerType: this.randomChoice(['个人', '企业', '组织']),
            historyCount: Math.floor(Math.random() * 10),
            
            // 处理信息 - 多部门协同模式下承办单位等于主办部门
            department: isCollaborative ? mainDepartment : this.randomChoice(this.departments),
            currentHandler: Math.random() < 0.7 ? this.randomChoice(this.citizenNames) : null,
            area: this.randomChoice(this.areas),
            
            // 时间信息
            createTime: createTime,
            dispatchTime: dispatchTime,
            acceptTime: acceptTime,
            finishTime: finishTime,
            timeLimit: timeLimit,
            currentStageTimeLimit: createTime + Math.floor(Math.random() * 24 * 60 * 60 * 1000), // 当前环节时限

            // 业务流程信息
            currentStage: this.randomChoice(['受理阶段', '派单阶段', '处理阶段', '审核阶段', '回访阶段']),
            flowPath: this.randomChoice(['市级→区级→街镇', '市级→部门', '区级→街镇→社区']),
            instantHandleOpportunity: Math.random() < 0.3 ? '可即时办结' : '需要流转',
            handleMethod: this.randomChoice(['即时办结', '现场办结', '协调办结', '流转办结']),

            // 超时状态
            overtimeStatus: timeLimit < Date.now() ? '已超时' :
                           (timeLimit - Date.now()) < (4 * 60 * 60 * 1000) ? '即将超时' : '正常',
            
            // 统计信息
            followCount: Math.floor(Math.random() * 5),
            callbackStatus: this.randomChoice(['pending', 'completed', 'not_required']),
            satisfaction: status === 'closed' ? Math.floor(Math.random() * 5) + 1 : null,
            callbackResult: Math.random() < 0.7 ? this.randomChoice(['满意', '基本满意', '不满意', '未回访']) : '未回访',
            restartCount: Math.floor(Math.random() * 3),
            
            // 关联信息
            relationType: relationType,
            relationCount: relationCount,
            parentTicketId: parentTicketId,
            
            // 协办信息
            collaborative: collaborativeInfo,
            collaborationMode: isCollaborative ? '多部门协同' : '单独处理',
            mainDepartment: isCollaborative ? mainDepartment : null,
            assistDepartments: isCollaborative && assistDepartments ? assistDepartments.join(', ') : null,
            
            // 督办信息
            supervise: mode === 'supervise' ? {
                level: this.randomChoice(['municipal', 'district', 'street']),
                reason: this.randomChoice(['timeout', 'complaint', 'media', 'leader']),
                supervisor: this.randomChoice(this.citizenNames),
                superviseTime: createTime + Math.floor(Math.random() * 24 * 60 * 60 * 1000)
            } : null,
            supervisionLevel: Math.random() < 0.15 ? this.randomChoice(['市级督办', '区级督办', '街镇督办']) : '无',
            
            // 其他信息
            priority: this.calculatePriority(urgency, createTime, timeLimit),
            tags: this.generateTags(status, urgency, mode, specialTags),
            canMerge: Math.random() < 0.2, // 20%的工单可以合并
            canSplit: Math.random() < 0.1,  // 10%的工单可以拆分
            
            // 地理信息
            coordinates: {
                lng: 116.3974 + (Math.random() - 0.5) * 0.1,
                lat: 39.9093 + (Math.random() - 0.5) * 0.1
            }
        };
    },
    
    // 计算优先级
    calculatePriority(urgency, createTime, timeLimit) {
        const now = Date.now();
        const timeRemaining = timeLimit - now;
        const totalTime = timeLimit - createTime;
        const timeRatio = timeRemaining / totalTime;
        
        let priority = 0;
        
        // 紧急程度权重
        if (urgency === 'critical') priority += 30;
        else if (urgency === 'urgent') priority += 20;
        else priority += 10;
        
        // 时限权重
        if (timeRatio < 0) priority += 25; // 已超时
        else if (timeRatio < 0.1) priority += 20; // 即将超时
        else if (timeRatio < 0.2) priority += 15; // 时间紧张
        else priority += 5;
        
        return priority;
    },
    
    // 生成标签
    generateTags(status, urgency, mode, specialTags) {
        const tags = [];
        
        // 状态标签
        tags.push({
            type: 'status',
            value: status,
            label: this.getStatusLabel(status),
            color: CONFIG.colors.status[status]
        });
        
        // 紧急程度标签
        if (urgency !== 'normal') {
            tags.push({
                type: 'urgency',
                value: urgency,
                label: this.getUrgencyLabel(urgency),
                color: CONFIG.colors.urgency[urgency]
            });
        }
        
        // 处理模式标签
        if (mode !== 'normal') {
            tags.push({
                type: 'mode',
                value: mode,
                label: this.getModeLabel(mode),
                color: CONFIG.colors.mode[mode]
            });
        }
        
        // 特殊标签
        specialTags.forEach(tag => {
            tags.push({
                type: 'special',
                value: tag,
                label: this.getSpecialLabel(tag),
                color: '#722ed1'
            });
        });
        
        return tags;
    },
    
    // 获取状态标签
    getStatusLabel(status) {
        const labels = {
            draft: '草稿',
            pending: '待接收',
            processing: '处理中',
            review: '待审核',
            callback: '待回访',
            closed: '已关闭',
            cancelled: '已废除',
            suspended: '挂起'
        };
        return labels[status] || status;
    },
    
    // 获取紧急程度标签
    getUrgencyLabel(urgency) {
        const labels = {
            normal: '一般',
            urgent: '紧急',
            critical: '特急'
        };
        return labels[urgency] || urgency;
    },
    
    // 获取处理模式标签
    getModeLabel(mode) {
        const labels = {
            instant: '即时办结',
            normal: '普通流转',
            collaborative: '多部门协同',
            supervise: '督办'
        };
        return labels[mode] || mode;
    },
    
    // 获取特殊标签
    getSpecialLabel(tag) {
        const labels = {
            vip: 'VIP客户',
            repeat: '重复投诉',
            media: '媒体关注',
            leader: '领导批示'
        };
        return labels[tag] || tag;
    },
    
    // 生成工单列表
    generateTickets(count = 100) {
        const tickets = [];
        for (let i = 1; i <= count; i++) {
            tickets.push(this.generateTicket(i));
        }
        return tickets;
    },
    
    // 生成统计数据
    generateStats(tickets) {
        const stats = {
            total: tickets.length,
            urgent: tickets.filter(t => t.urgency === 'critical').length,
            timeout: tickets.filter(t => t.timeLimit < Date.now()).length,
            pending: tickets.filter(t => t.status === 'pending').length,
            merge: tickets.filter(t => t.canMerge).length
        };
        
        return stats;
    }
};

// 导出模拟数据（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MockData;
}
