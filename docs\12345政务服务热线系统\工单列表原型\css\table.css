/* 表格样式 */

.ticket-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    table-layout: auto;
    min-width: 3000px;
}

/* 表头样式 */
.ticket-table thead {
    background-color: var(--table-header-bg);
    position: sticky;
    top: 0;
    z-index: 10;
}

.ticket-table th {
    padding: var(--table-cell-padding);
    text-align: left;
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    border-bottom: 1px solid var(--table-border);
    white-space: nowrap;
    user-select: none;
}

.ticket-table th.sortable {
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.ticket-table th.sortable:hover {
    background-color: var(--bg-light);
}

.ticket-table th.sortable.sorted-asc .sort-icon::after {
    content: '↑';
    color: var(--primary-color);
}

.ticket-table th.sortable.sorted-desc .sort-icon::after {
    content: '↓';
    color: var(--primary-color);
}

.sort-icon {
    margin-left: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-disabled);
    transition: color var(--transition-normal);
}

.sort-icon::after {
    content: '↕️';
}

/* 固定列样式 */
.checkbox-col {
    width: 50px;
    text-align: center;
    position: sticky;
    left: 0;
    background-color: var(--table-header-bg);
    z-index: 20;
    border-right: 1px solid var(--border-color);
}

.ticket-table tbody .checkbox-col {
    background-color: var(--bg-white);
}

/* 工单编号列 - 左侧固定 */
.ticket-table th:nth-child(2),
.ticket-table td:nth-child(2) {
    width: 160px;
    position: sticky;
    left: 50px;
    background-color: var(--table-header-bg);
    z-index: 20;
    border-right: 1px solid var(--border-color);
}

.ticket-table tbody td:nth-child(2) {
    background-color: var(--bg-white);
}

/* 操作列 - 右侧固定 */
.actions-col {
    width: 140px;
    text-align: center;
    position: sticky;
    right: 0;
    background-color: var(--table-header-bg);
    z-index: 20;
    border-left: 1px solid var(--border-color);
}

.ticket-table tbody .actions-col {
    background-color: var(--bg-white);
}

/* 设置各列的宽度 - 完整字段布局 */
.ticket-table th:nth-child(3) { width: 100px; } /* 状态 */
.ticket-table th:nth-child(4) { width: 120px; } /* 处理模式 */
.ticket-table th:nth-child(5) { width: 100px; } /* 紧急程度 */
.ticket-table th:nth-child(6) { width: 100px; } /* 督办标识 */
.ticket-table th:nth-child(7) { width: 120px; } /* 当前环节 */
.ticket-table th:nth-child(8) { width: 100px; } /* 协办模式 */
.ticket-table th:nth-child(9) { width: 100px; } /* 关联类型 */
.ticket-table th:nth-child(10) { width: 300px; } /* 诉求标题 */
.ticket-table th:nth-child(11) { width: 150px; } /* 市民信息 */
.ticket-table th:nth-child(12) { width: 100px; } /* VIP标识 */
.ticket-table th:nth-child(13) { width: 80px; } /* 客户类型 */
.ticket-table th:nth-child(14) { width: 80px; } /* 历史工单 */
.ticket-table th:nth-child(15) { width: 150px; } /* 承办单位 */
.ticket-table th:nth-child(16) { width: 150px; } /* 主办单位 */
.ticket-table th:nth-child(17) { width: 180px; } /* 协办单位 */
.ticket-table th:nth-child(18) { width: 150px; } /* 涉及区域 */
.ticket-table th:nth-child(19) { width: 120px; } /* 创建时间 */
.ticket-table th:nth-child(20) { width: 120px; } /* 剩余时限 */
.ticket-table th:nth-child(21) { width: 120px; } /* 环节时限 */
.ticket-table th:nth-child(22) { width: 100px; } /* 超时状态 */
.ticket-table th:nth-child(23) { width: 80px; } /* 满意度 */
.ticket-table th:nth-child(24) { width: 100px; } /* 回访结果 */
.ticket-table th:nth-child(25) { width: 80px; } /* 重启次数 */

/* 对应的td列也设置相同宽度 */
.ticket-table td:nth-child(3) { width: 100px; }
.ticket-table td:nth-child(4) { width: 120px; }
.ticket-table td:nth-child(5) { width: 100px; }
.ticket-table td:nth-child(6) { width: 100px; }
.ticket-table td:nth-child(7) { width: 120px; }
.ticket-table td:nth-child(8) { width: 100px; }
.ticket-table td:nth-child(9) { width: 100px; }
.ticket-table td:nth-child(10) { width: 300px; }
.ticket-table td:nth-child(11) { width: 150px; }
.ticket-table td:nth-child(12) { width: 100px; }
.ticket-table td:nth-child(13) { width: 80px; }
.ticket-table td:nth-child(14) { width: 80px; }
.ticket-table td:nth-child(15) { width: 150px; }
.ticket-table td:nth-child(16) { width: 150px; }
.ticket-table td:nth-child(17) { width: 180px; }
.ticket-table td:nth-child(18) { width: 150px; }
.ticket-table td:nth-child(19) { width: 120px; }
.ticket-table td:nth-child(20) { width: 120px; }
.ticket-table td:nth-child(21) { width: 120px; }
.ticket-table td:nth-child(22) { width: 100px; }
.ticket-table td:nth-child(23) { width: 80px; }
.ticket-table td:nth-child(24) { width: 100px; }
.ticket-table td:nth-child(25) { width: 80px; }

/* 表格行样式 */
.ticket-table tbody tr {
    border-bottom: 1px solid var(--table-border);
    transition: all var(--transition-normal);
    cursor: pointer;
}

/* 新增字段样式 */
.supervision-cell .supervision-level.has-supervision {
    color: var(--error-color);
    font-weight: var(--font-weight-semibold);
}

.collaboration-cell .collaboration-mode.has-collaboration {
    color: var(--success-color);
    font-weight: var(--font-weight-medium);
}

.relation-cell .relation-type.has-relation {
    color: var(--info-color);
    font-weight: var(--font-weight-medium);
}

.vip-cell .vip-level.is-vip {
    color: var(--warning-color);
    font-weight: var(--font-weight-semibold);
}

.history-cell .history-count.frequent {
    color: var(--error-color);
    font-weight: var(--font-weight-semibold);
}

.overtime-cell .overtime-status.已超时 {
    color: var(--error-color);
    font-weight: var(--font-weight-semibold);
}

.overtime-cell .overtime-status.即将超时 {
    color: var(--warning-color);
    font-weight: var(--font-weight-medium);
}

.overtime-cell .overtime-status.正常 {
    color: var(--success-color);
}

.satisfaction-cell .satisfaction-score.high {
    color: var(--success-color);
}

.satisfaction-cell .satisfaction-score.medium {
    color: var(--warning-color);
}

.satisfaction-cell .satisfaction-score.low {
    color: var(--error-color);
}

.callback-cell .callback-result.满意 {
    color: var(--success-color);
}

.callback-cell .callback-result.不满意 {
    color: var(--error-color);
}

.restart-cell .restart-count.has-restart {
    color: var(--error-color);
    font-weight: var(--font-weight-semibold);
}

.time-expired {
    color: var(--error-color);
    font-weight: var(--font-weight-semibold);
}

.time-urgent {
    color: var(--warning-color);
    font-weight: var(--font-weight-medium);
}

.time-normal {
    color: var(--success-color);
}

/* 多部门协同工单特殊样式 */
.main-dept-cell, .assist-dept-cell {
    font-size: var(--font-size-xs);
}

.main-department, .assist-departments {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 空值显示样式 */
.main-department:empty::after,
.assist-departments:empty::after {
    content: '-';
    color: var(--text-disabled);
}

/* 协办单位列表样式 */
.assist-departments[title]:not([title=""]):hover {
    cursor: help;
}

.ticket-table tbody tr:hover {
    background-color: var(--table-row-hover);
}

.ticket-table tbody tr:hover .checkbox-col,
.ticket-table tbody tr:hover td:nth-child(2),
.ticket-table tbody tr:hover .actions-col {
    background-color: var(--table-row-hover);
}

.ticket-table tbody tr.selected {
    background-color: var(--row-selected);
}

.ticket-table tbody tr.selected .checkbox-col,
.ticket-table tbody tr.selected td:nth-child(2),
.ticket-table tbody tr.selected .actions-col {
    background-color: var(--row-selected);
}

.ticket-table tbody tr.urgent {
    background-color: var(--row-urgent);
    border-left: 3px solid var(--error-color);
}

.ticket-table tbody tr.timeout {
    background-color: var(--row-timeout);
    border-left: 3px solid var(--warning-color);
}

.ticket-table tbody tr.disabled {
    background-color: var(--row-disabled);
    opacity: 0.6;
}

/* 表格单元格样式 */
.ticket-table td {
    padding: var(--table-cell-padding);
    vertical-align: middle;
    border-bottom: 1px solid var(--table-border);
}

.ticket-table td.checkbox-col {
    text-align: center;
}

.ticket-table td.actions-col {
    text-align: center;
}

/* 工单编号列 */
.ticket-number {
    font-family: 'Courier New', monospace;
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    cursor: pointer;
}

.ticket-number:hover {
    text-decoration: underline;
}

/* 状态列 */
.status-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-text {
    font-weight: var(--font-weight-medium);
}

/* 处理模式列 */
.mode-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 紧急程度列 */
.urgency-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 诉求标题列 */
.title-cell {
    max-width: 200px;
}

.title-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.title-text:hover {
    color: var(--primary-color);
}

/* 市民信息列 */
.citizen-cell {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.citizen-name {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.citizen-phone {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.citizen-tags {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: 2px;
}

.citizen-tag {
    padding: 1px 4px;
    font-size: 10px;
    font-weight: var(--font-weight-medium);
    border-radius: 2px;
    white-space: nowrap;
}

.citizen-tag.vip {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.citizen-tag.repeat {
    background-color: var(--error-light);
    color: var(--error-color);
}

.citizen-tag.media {
    background-color: var(--info-light);
    color: var(--info-color);
}

/* 承办单位列 */
.department-cell {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.department-name {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.department-person {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.collaborative-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: 2px;
}

.collaborative-tag {
    padding: 1px 4px;
    font-size: 10px;
    font-weight: var(--font-weight-medium);
    border-radius: 2px;
    white-space: nowrap;
}

.collaborative-tag.main {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.collaborative-tag.assist {
    background-color: var(--success-light);
    color: var(--success-color);
}

/* 区域列 */
.area-cell {
    max-width: 150px;
}

.area-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--text-color);
}

/* 时间列 */
.time-cell {
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

/* 时限列 */
.time-limit-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.time-limit-text.normal {
    color: var(--success-color);
}

.time-limit-text.warning {
    color: var(--warning-color);
}

.time-limit-text.danger {
    color: var(--error-color);
}

.time-limit-text.timeout {
    color: var(--error-color);
    animation: blink 1s infinite;
}

/* 操作列 */
.actions-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.action-btn.view {
    color: var(--info-color);
}

.action-btn.view:hover {
    color: var(--info-color);
    background-color: var(--info-light);
    border-color: var(--info-color);
}

.action-btn.edit {
    color: var(--warning-color);
}

.action-btn.edit:hover {
    color: var(--warning-color);
    background-color: var(--warning-light);
    border-color: var(--warning-color);
}

.action-btn.dispatch {
    color: var(--primary-color);
}

.action-btn.dispatch:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.action-btn.close {
    color: var(--error-color);
}

.action-btn.close:hover {
    color: var(--error-color);
    background-color: var(--error-light);
    border-color: var(--error-color);
}

/* 更多操作下拉菜单 */
.more-actions {
    position: relative;
    display: inline-block;
}

.more-actions-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.more-actions.active .more-actions-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.more-actions-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.more-actions-item:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.more-actions-item:first-child {
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.more-actions-item:last-child {
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

.more-actions-item + .more-actions-item {
    border-top: 1px solid var(--border-light);
}

/* 特殊行样式 - 通过背景色区分，不添加图标 */
.ticket-table tbody tr.merged {
    background-color: rgba(24, 144, 255, 0.05);
    border-left: 3px solid var(--info-color);
}

.ticket-table tbody tr.merged .checkbox-col,
.ticket-table tbody tr.merged td:nth-child(2),
.ticket-table tbody tr.merged .actions-col {
    background-color: rgba(24, 144, 255, 0.05);
}

.ticket-table tbody tr.split {
    background-color: rgba(82, 196, 26, 0.05);
    border-left: 3px solid var(--success-color);
}

.ticket-table tbody tr.split .checkbox-col,
.ticket-table tbody tr.split td:nth-child(2),
.ticket-table tbody tr.split .actions-col {
    background-color: rgba(82, 196, 26, 0.05);
}

.ticket-table tbody tr.supervised {
    background-color: rgba(255, 77, 79, 0.05);
    border-left: 3px solid var(--error-color);
}

.ticket-table tbody tr.supervised .checkbox-col,
.ticket-table tbody tr.supervised td:nth-child(2),
.ticket-table tbody tr.supervised .actions-col {
    background-color: rgba(255, 77, 79, 0.05);
}

/* 响应式表格 - 保持所有列显示，使用水平滚动 */
@media (max-width: 768px) {
    .ticket-table {
        font-size: var(--font-size-xs);
    }

    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* 移动端显示提示 */
    .table-container::before {
        content: "← 左右滑动查看更多列 →";
        display: block;
        text-align: center;
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        background-color: var(--bg-light);
        border-bottom: 1px solid var(--border-light);
    }
}
