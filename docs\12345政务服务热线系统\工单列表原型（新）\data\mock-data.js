/**
 * 工单列表模拟数据
 * 包含各种状态、角色和业务场景的工单数据
 */

// 工单状态枚举
const TICKET_STATUS = {
    DRAFT: 'draft',           // 草稿/暂存
    PENDING: 'pending',       // 待接收
    PROCESSING: 'processing', // 处理中
    REVIEWING: 'reviewing',   // 待审核
    CALLBACK: 'callback',     // 待回访
    CLOSED: 'closed',         // 已关闭
    CANCELLED: 'cancelled'    // 已废除
};

// 紧急程度枚举
const URGENCY_LEVEL = {
    NORMAL: 'normal',     // 一般
    URGENT: 'urgent',     // 紧急
    CRITICAL: 'critical'  // 特急
};

// 处理模式枚举
const PROCESS_MODE = {
    INSTANT: 'instant',      // 即时办结
    NORMAL: 'normal',        // 普通流转
    COOPERATION: 'cooperation' // 多部门协同模式
};

// 督办级别枚举
const SUPERVISE_LEVEL = {
    NONE: 'none',        // 无督办
    GENERAL: 'general',  // 一般督办
    IMPORTANT: 'important', // 重点督办
    LEADER: 'leader'     // 领导督办
};

// 模拟工单数据
const mockTickets = [
    {
        id: 1,
        ticketNo: 'WD20241201000001',
        status: TICKET_STATUS.PROCESSING,
        mode: PROCESS_MODE.INSTANT,
        urgency: URGENCY_LEVEL.CRITICAL,
        supervise: SUPERVISE_LEVEL.LEADER,
        citizen: {
            name: '王**',
            phone: '138****5678',
            isVip: true,
            vipType: '人大代表',
            historyCount: 3
        },
        content: {
            title: '小区供暖设施故障，居民反映暖气不热',
            category: '供暖问题',
            description: '某某小区多栋楼暖气不热，影响居民正常生活...',
            address: '朝阳区某某街道某某小区'
        },
        timeLimit: {
            total: '7天',
            current: '2小时',
            remaining: '1小时30分',
            isOvertime: false,
            warningLevel: 'danger'
        },
        currentStep: '街道办事处处理中',
        flowPath: '市级→朝阳区→某某街道',
        assignedUnit: '某某街道办事处',
        handler: '李四',
        createTime: '2024-12-01 09:30:00',
        updateTime: '2024-12-01 14:20:00',
        cooperation: null,
        tags: ['供暖', '民生', '紧急'],
        satisfaction: null,
        restartCount: 0
    },
    {
        id: 2,
        ticketNo: 'WD20241201000002',
        status: TICKET_STATUS.PENDING,
        mode: PROCESS_MODE.COOPERATION,
        urgency: URGENCY_LEVEL.URGENT,
        supervise: SUPERVISE_LEVEL.IMPORTANT,
        citizen: {
            name: '张**',
            phone: '139****1234',
            isVip: false,
            vipType: null,
            historyCount: 1
        },
        content: {
            title: '道路积水严重，影响交通出行',
            category: '市政设施',
            description: '某某路段积水严重，车辆无法正常通行...',
            address: '海淀区某某路与某某街交叉口'
        },
        timeLimit: {
            total: '5天',
            current: '24小时',
            remaining: '3天2小时',
            isOvertime: false,
            warningLevel: 'warning'
        },
        currentStep: '待海淀区接收',
        flowPath: '市级→海淀区',
        assignedUnit: '海淀区12345中心',
        handler: null,
        createTime: '2024-12-01 08:15:00',
        updateTime: '2024-12-01 08:15:00',
        cooperation: {
            mode: '多部门协同',
            mainUnit: '海淀区市政局',
            cooperateUnits: ['海淀区交通委', '海淀区应急局'],
            progress: {
                main: 'pending',
                cooperate: ['pending', 'pending']
            }
        },
        tags: ['市政', '交通', '积水'],
        satisfaction: null,
        restartCount: 0
    },
    {
        id: 3,
        ticketNo: 'WD20241130000156',
        status: TICKET_STATUS.CALLBACK,
        mode: PROCESS_MODE.NORMAL,
        urgency: URGENCY_LEVEL.NORMAL,
        supervise: SUPERVISE_LEVEL.NONE,
        citizen: {
            name: '刘**',
            phone: '136****9876',
            isVip: false,
            vipType: null,
            historyCount: 0
        },
        content: {
            title: '噪音扰民投诉',
            category: '环境问题',
            description: '楼下商铺夜间营业噪音过大，影响休息...',
            address: '西城区某某胡同某号'
        },
        timeLimit: {
            total: '7天',
            current: '回访期',
            remaining: '2天',
            isOvertime: false,
            warningLevel: 'normal'
        },
        currentStep: '待回访',
        flowPath: '市级→西城区→某某街道→社区',
        assignedUnit: '某某社区',
        handler: '王五',
        createTime: '2024-11-30 16:45:00',
        updateTime: '2024-12-01 10:30:00',
        cooperation: null,
        tags: ['噪音', '环境', '商铺'],
        satisfaction: null,
        restartCount: 0
    },
    {
        id: 4,
        ticketNo: 'WD20241130000145',
        status: TICKET_STATUS.CLOSED,
        mode: PROCESS_MODE.INSTANT,
        urgency: URGENCY_LEVEL.NORMAL,
        supervise: SUPERVISE_LEVEL.NONE,
        citizen: {
            name: '陈**',
            phone: '135****4567',
            isVip: false,
            vipType: null,
            historyCount: 2
        },
        content: {
            title: '咨询办事流程',
            category: '咨询服务',
            description: '咨询如何办理居住证...',
            address: '丰台区'
        },
        timeLimit: {
            total: '即时',
            current: '已完成',
            remaining: '已办结',
            isOvertime: false,
            warningLevel: 'success'
        },
        currentStep: '已关闭',
        flowPath: '市级即时办结',
        assignedUnit: '市级12345中心',
        handler: '张三',
        createTime: '2024-11-30 14:20:00',
        updateTime: '2024-11-30 14:25:00',
        cooperation: null,
        tags: ['咨询', '居住证', '即时办结'],
        satisfaction: {
            score: 5,
            level: '满意',
            comment: '回答详细，服务态度好'
        },
        restartCount: 0
    },
    {
        id: 5,
        ticketNo: 'WD20241130000134',
        status: TICKET_STATUS.PROCESSING,
        mode: PROCESS_MODE.NORMAL,
        urgency: URGENCY_LEVEL.URGENT,
        supervise: SUPERVISE_LEVEL.GENERAL,
        citizen: {
            name: '赵**',
            phone: '137****8901',
            isVip: true,
            vipType: '政协委员',
            historyCount: 5
        },
        content: {
            title: '老旧小区电梯故障频发',
            category: '设施维护',
            description: '小区电梯经常故障，老人上下楼困难...',
            address: '东城区某某小区'
        },
        timeLimit: {
            total: '7天',
            current: '3天',
            remaining: '4天',
            isOvertime: false,
            warningLevel: 'normal'
        },
        currentStep: '东城区住建委处理中',
        flowPath: '市级→东城区→住建委',
        assignedUnit: '东城区住建委',
        handler: '孙六',
        createTime: '2024-11-30 11:10:00',
        updateTime: '2024-12-01 09:45:00',
        cooperation: null,
        tags: ['电梯', '老旧小区', '设施维护'],
        satisfaction: null,
        restartCount: 1
    }
];

// 用户角色数据
const userRoles = {
    'city-operator': {
        name: '市级话务员',
        permissions: ['create', 'assign', 'merge', 'split', 'instant-resolve'],
        visibleFields: ['all'],
        quickFilters: ['instant-opportunity', 'similar-tickets', 'complex-tickets']
    },
    'district-manager': {
        name: '区级管理者',
        permissions: ['supervise', 'reassign', 'audit'],
        visibleFields: ['management'],
        quickFilters: ['supervise-tickets', 'overtime-tickets', 'audit-pending']
    },
    'executor': {
        name: '执行人员',
        permissions: ['accept', 'process', 'submit'],
        visibleFields: ['execution'],
        quickFilters: ['my-tasks', 'urgent-tasks', 'cooperation-tasks']
    },
    'callback-staff': {
        name: '回访员',
        permissions: ['callback', 'close', 'restart'],
        visibleFields: ['callback'],
        quickFilters: ['pending-callback', 'high-risk', 'unsatisfied']
    }
};

// 统计数据
const statisticsData = {
    total: 1234,
    myTodo: 23,
    urgent: 5,
    overtime: 12,
    supervise: 8,
    draft: 3,
    pending: 156,
    processing: 789,
    reviewing: 45,
    callback: 234,
    closed: 7
};

// 导出数据
window.mockData = {
    tickets: mockTickets,
    userRoles: userRoles,
    statistics: statisticsData,
    enums: {
        TICKET_STATUS,
        URGENCY_LEVEL,
        PROCESS_MODE,
        SUPERVISE_LEVEL
    }
};

// 模拟API函数
window.mockAPI = {
    /**
     * 获取工单列表
     * @param {Object} params 查询参数
     * @returns {Promise} 返回工单列表数据
     */
    getTickets: function(params = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                let filteredTickets = [...mockTickets];
                
                // 应用筛选条件
                if (params.status) {
                    filteredTickets = filteredTickets.filter(ticket => ticket.status === params.status);
                }
                if (params.urgency) {
                    filteredTickets = filteredTickets.filter(ticket => ticket.urgency === params.urgency);
                }
                if (params.mode) {
                    filteredTickets = filteredTickets.filter(ticket => ticket.mode === params.mode);
                }
                if (params.search) {
                    const searchTerm = params.search.toLowerCase();
                    filteredTickets = filteredTickets.filter(ticket => 
                        ticket.ticketNo.toLowerCase().includes(searchTerm) ||
                        ticket.citizen.name.toLowerCase().includes(searchTerm) ||
                        ticket.content.title.toLowerCase().includes(searchTerm)
                    );
                }
                
                // 应用排序
                if (params.sortBy) {
                    filteredTickets.sort((a, b) => {
                        const aVal = this.getNestedValue(a, params.sortBy);
                        const bVal = this.getNestedValue(b, params.sortBy);
                        const order = params.sortOrder === 'desc' ? -1 : 1;
                        return aVal > bVal ? order : aVal < bVal ? -order : 0;
                    });
                }
                
                // 应用分页
                const page = params.page || 1;
                const pageSize = params.pageSize || 50;
                const start = (page - 1) * pageSize;
                const end = start + pageSize;
                const paginatedTickets = filteredTickets.slice(start, end);
                
                resolve({
                    data: paginatedTickets,
                    total: filteredTickets.length,
                    page: page,
                    pageSize: pageSize,
                    totalPages: Math.ceil(filteredTickets.length / pageSize)
                });
            }, 300); // 模拟网络延迟
        });
    },
    
    /**
     * 获取嵌套对象的值
     * @param {Object} obj 对象
     * @param {String} path 路径
     * @returns {*} 值
     */
    getNestedValue: function(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    },
    
    /**
     * 获取工单详情
     * @param {String} ticketId 工单ID
     * @returns {Promise} 返回工单详情
     */
    getTicketDetail: function(ticketId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const ticket = mockTickets.find(t => t.id == ticketId);
                resolve(ticket);
            }, 200);
        });
    },
    
    /**
     * 获取统计数据
     * @returns {Promise} 返回统计数据
     */
    getStatistics: function() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(statisticsData);
            }, 100);
        });
    }
};
