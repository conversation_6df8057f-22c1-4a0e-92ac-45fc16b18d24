<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单列表 - 12345智慧政务平台</title>
    
    <!-- CSS 文件 -->
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/table.css">
    <link rel="stylesheet" href="css/filters.css">
    <link rel="stylesheet" href="css/modals.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="page-header">
        <div class="header-left">
            <h1 class="page-title">工单列表</h1>
            <div class="breadcrumb">
                <span>首页</span>
                <span class="separator">/</span>
                <span>工单管理</span>
                <span class="separator">/</span>
                <span class="current">工单列表</span>
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <span class="user-role" id="userRole">市级话务员</span>
                <span class="user-name">张三</span>
                <div class="user-avatar">张</div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 工作台统计面板 -->
        <section class="dashboard-panel" id="dashboardPanel">
            <div class="stat-cards">
                <div class="stat-card urgent">
                    <div class="stat-icon">🔥</div>
                    <div class="stat-content">
                        <div class="stat-number" id="urgentCount">12</div>
                        <div class="stat-label">特急工单</div>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <div class="stat-number" id="timeoutCount">8</div>
                        <div class="stat-label">即将超时</div>
                    </div>
                </div>
                <div class="stat-card pending">
                    <div class="stat-icon">📥</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pendingCount">156</div>
                        <div class="stat-label">待处理</div>
                    </div>
                </div>
                <div class="stat-card merge">
                    <div class="stat-icon">🔗</div>
                    <div class="stat-content">
                        <div class="stat-number" id="mergeCount">5</div>
                        <div class="stat-label">可合并</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选和搜索区域 -->
        <section class="filter-section">
            <div class="filter-toolbar">
                <!-- 快速筛选和搜索 -->
                <div class="quick-filters">
                    <div class="filter-group">
                        <label>状态筛选：</label>
                        <div class="filter-buttons" id="statusFilters">
                            <button class="filter-btn active" data-value="">全部</button>
                            <button class="filter-btn" data-value="draft">草稿</button>
                            <button class="filter-btn" data-value="pending">待接收</button>
                            <button class="filter-btn" data-value="processing">处理中</button>
                            <button class="filter-btn" data-value="review">待审核</button>
                            <button class="filter-btn" data-value="callback">待回访</button>
                            <button class="filter-btn" data-value="closed">已关闭</button>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label>紧急程度：</label>
                        <div class="filter-buttons" id="urgencyFilters">
                            <button class="filter-btn active" data-value="">全部</button>
                            <button class="filter-btn" data-value="normal">一般</button>
                            <button class="filter-btn" data-value="urgent">紧急</button>
                            <button class="filter-btn" data-value="critical">特急</button>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label>处理模式：</label>
                        <div class="filter-buttons" id="modeFilters">
                            <button class="filter-btn active" data-value="">全部</button>
                            <button class="filter-btn" data-value="instant">即时办结</button>
                            <button class="filter-btn" data-value="normal">普通流转</button>
                            <button class="filter-btn" data-value="collaborative">多部门协同</button>
                            <button class="filter-btn" data-value="supervise">督办</button>
                        </div>
                    </div>

                    <!-- 搜索区域 -->
                    <div class="search-area">
                        <div class="search-input-group">
                            <input type="text" class="search-input" id="searchInput" placeholder="搜索工单编号、市民姓名、问题关键词...">
                            <button class="search-btn" id="searchBtn">🔍</button>
                        </div>
                        <button class="advanced-search-btn" id="advancedSearchBtn">高级搜索</button>
                    </div>
                </div>
            </div>

            <!-- 角色专用筛选 - 暂时隐藏，通过高级搜索提供 -->
            <!--
            <div class="role-specific-filters" id="roleSpecificFilters" style="display: none;">
                <div class="role-filter municipal-operator">
                    <div class="filter-group">
                        <label>处理策略：</label>
                        <select class="filter-select" id="strategyFilter">
                            <option value="">全部</option>
                            <option value="instant">可即时办结</option>
                            <option value="dispatch">需要派单</option>
                            <option value="complex">复杂协调</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>合并机会：</label>
                        <select class="filter-select" id="mergeFilter">
                            <option value="">全部</option>
                            <option value="similar">有相似工单</option>
                            <option value="mergeable">可合并工单</option>
                            <option value="independent">独立工单</option>
                        </select>
                    </div>
                </div>
            </div>
            -->
        </section>

        <!-- 工具栏 -->
        <section class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-primary" id="newTicketBtn">
                    <span class="btn-icon">➕</span>
                    <span class="btn-text">新建工单</span>
                </button>
                <button class="btn btn-secondary" id="refreshBtn">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">刷新</span>
                </button>
                <button class="btn btn-outline" id="batchMergeBtn" disabled>
                    <span class="btn-icon">🔗</span>
                    <span class="btn-text">批量合并</span>
                </button>
                <button class="btn btn-outline" id="batchDispatchBtn" disabled>
                    <span class="btn-icon">📤</span>
                    <span class="btn-text">批量派单</span>
                </button>
            </div>
            <div class="toolbar-right">
                <div class="view-controls">
                    <button class="view-btn active" data-view="table" title="表格视图">📋</button>
                    <button class="view-btn" data-view="card" title="卡片视图">🗃️</button>
                </div>
                <button class="btn btn-outline" id="columnSettingsBtn">
                    <span class="btn-icon">⚙️</span>
                    <span class="btn-text">列设置</span>
                </button>
                <button class="btn btn-outline" id="exportBtn">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">导出</span>
                </button>
            </div>
        </section>

        <!-- 批量操作提示 -->
        <div class="batch-info" id="batchInfo" style="display: none;">
            <div class="batch-content">
                <span class="batch-text">已选择 <span id="selectedCount">0</span> 个工单</span>
                <div class="batch-actions">
                    <button class="btn btn-small btn-primary" id="batchProcessBtn">批量处理</button>
                    <button class="btn btn-small btn-outline" id="clearSelectionBtn">清除选择</button>
                </div>
            </div>
        </div>

        <!-- 工单列表表格 -->
        <section class="table-section">
            <!-- 顶部横向滚动条 -->
            <div class="top-scrollbar-container" id="topScrollbar">
                <div class="top-scrollbar-content" id="topScrollbarContent"></div>
            </div>

            <div class="table-container" id="tableContainer">
                <table class="ticket-table" id="ticketTable">
                    <thead>
                        <tr>
                            <th class="checkbox-col">
                                <input type="checkbox" id="selectAll" class="table-checkbox">
                            </th>
                            <th class="sortable" data-sort="ticketNumber">
                                工单编号
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="status">
                                状态
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="mode">
                                处理模式
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="urgency">
                                紧急程度
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="supervisionLevel">
                                督办标识
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="currentStage">
                                当前环节
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="collaborationMode">
                                协办模式
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="relationType">
                                关联类型
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="title">
                                诉求标题
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="citizen">
                                市民信息
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="vipLevel">
                                VIP标识
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="customerType">
                                客户类型
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="historyCount">
                                历史工单
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="department">
                                承办单位
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="mainDepartment">
                                主办单位
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="assistDepartments">
                                协办单位
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="area">
                                涉及区域
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="createTime">
                                创建时间
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="timeLimit">
                                剩余时限
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="currentStageTimeLimit">
                                环节时限
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="overtimeStatus">
                                超时状态
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="satisfaction">
                                满意度
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="callbackResult">
                                回访结果
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="restartCount">
                                重启次数
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="actions-col">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ticketTableBody">
                        <!-- 工单数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" id="loadingState" style="display: none;">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载工单数据...</div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">📋</div>
                <div class="empty-text">暂无工单数据</div>
                <div class="empty-desc">请调整筛选条件或创建新工单</div>
            </div>
        </section>

        <!-- 分页控件 -->
        <section class="pagination-section">
            <div class="pagination-info">
                <span>共 <span id="totalCount">0</span> 条记录，每页显示</span>
                <select class="page-size-select" id="pageSizeSelect">
                    <option value="20">20</option>
                    <option value="50" selected>50</option>
                    <option value="100">100</option>
                </select>
                <span>条</span>
            </div>
            <div class="pagination-controls" id="paginationControls">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </section>
    </main>

    <!-- 高级搜索模态框 -->
    <div class="modal" id="advancedSearchModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>高级搜索</h3>
                <button class="modal-close" id="closeAdvancedSearch">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 高级搜索表单内容将在单独的JS文件中实现 -->
            </div>
        </div>
    </div>

    <!-- 列设置模态框 -->
    <div class="modal" id="columnSettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>列显示设置</h3>
                <button class="modal-close" id="closeColumnSettings">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 列设置内容将在单独的JS文件中实现 -->
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components/table.js"></script>
    <script src="js/components/filters.js"></script>
    <script src="js/components/modals.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/ticket-list.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
