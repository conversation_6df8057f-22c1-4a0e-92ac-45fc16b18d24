/**
 * 批量操作模块
 * 处理批量派单、批量审核、批量导出等功能
 */

window.BatchManager = {
    // 当前批量操作类型
    currentOperation: null,
    
    // 批量操作配置
    operations: {
        assign: {
            name: '批量派单',
            icon: 'share',
            requiresDialog: true,
            minSelection: 1
        },
        approve: {
            name: '批量审核',
            icon: 'check',
            requiresDialog: true,
            minSelection: 1
        },
        reject: {
            name: '批量退回',
            icon: 'times',
            requiresDialog: true,
            minSelection: 1
        },
        export: {
            name: '导出数据',
            icon: 'download',
            requiresDialog: false,
            minSelection: 0
        },
        print: {
            name: '批量打印',
            icon: 'print',
            requiresDialog: false,
            minSelection: 1
        },
        delete: {
            name: '批量删除',
            icon: 'trash',
            requiresDialog: true,
            minSelection: 1,
            dangerous: true
        }
    },
    
    /**
     * 初始化批量操作
     */
    init: function() {
        this.bindEvents();
        this.createDialogs();
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 批量操作按钮
        document.querySelectorAll('.batch-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const operation = e.currentTarget.dataset.operation;
                this.performBatchOperation(operation);
            });
        });
        
        // 清空选择按钮
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', () => {
                this.clearSelection();
            });
        }
    },
    
    /**
     * 执行批量操作
     * @param {string} operation 操作类型
     */
    performBatchOperation: function(operation) {
        const config = this.operations[operation];
        if (!config) {
            Utils.showMessage('未知的批量操作', 'error');
            return;
        }
        
        // 获取选中的工单
        const selectedTickets = window.TableManager ? window.TableManager.getSelectedTickets() : [];
        
        // 检查选择数量
        if (selectedTickets.length < config.minSelection) {
            Utils.showMessage(`请至少选择 ${config.minSelection} 个工单`, 'warning');
            return;
        }
        
        // 检查操作权限
        if (!this.checkOperationPermission(operation, selectedTickets)) {
            Utils.showMessage('您没有权限执行此操作', 'error');
            return;
        }
        
        this.currentOperation = operation;
        
        // 根据操作类型执行不同逻辑
        if (config.requiresDialog) {
            this.showOperationDialog(operation, selectedTickets);
        } else {
            this.executeOperation(operation, selectedTickets);
        }
    },
    
    /**
     * 检查操作权限
     * @param {string} operation 操作类型
     * @param {Array} tickets 工单列表
     * @returns {boolean} 是否有权限
     */
    checkOperationPermission: function(operation, tickets) {
        // 这里应该根据用户角色和工单状态检查权限
        // 简化实现，实际项目中需要更复杂的权限逻辑
        
        const userRole = window.currentUser ? window.currentUser.role : 'operator';
        
        switch (operation) {
            case 'assign':
                // 只有管理员和话务员可以派单
                return ['admin', 'operator'].includes(userRole);
            
            case 'approve':
            case 'reject':
                // 只有管理员可以批量审核
                return userRole === 'admin';
            
            case 'delete':
                // 只有管理员可以删除，且只能删除草稿状态的工单
                if (userRole !== 'admin') return false;
                return tickets.every(ticket => ticket.status === 'draft');
            
            case 'export':
            case 'print':
                // 所有用户都可以导出和打印
                return true;
            
            default:
                return false;
        }
    },
    
    /**
     * 显示操作对话框
     * @param {string} operation 操作类型
     * @param {Array} tickets 工单列表
     */
    showOperationDialog: function(operation, tickets) {
        const dialog = document.getElementById(`${operation}Dialog`);
        const overlay = document.getElementById('overlay');
        
        if (dialog && overlay) {
            // 更新对话框内容
            this.updateDialogContent(operation, tickets);

            // 显示对话框
            dialog.classList.add('active');
            overlay.classList.add('active');
        } else {
            // 如果没有对话框，直接执行操作
            this.executeOperation(operation, tickets);
        }
    },
    
    /**
     * 更新对话框内容
     * @param {string} operation 操作类型
     * @param {Array} tickets 工单列表
     */
    updateDialogContent: function(operation, tickets) {
        const dialog = document.getElementById(`${operation}Dialog`);
        if (!dialog) return;
        
        // 更新选中工单列表
        const ticketList = dialog.querySelector('.selected-tickets-list');
        if (ticketList) {
            ticketList.innerHTML = tickets.map(ticket => `
                <div class="selected-ticket-item">
                    <span class="ticket-no">${ticket.ticketNo}</span>
                    <span class="ticket-title">${ticket.content.title}</span>
                    <span class="ticket-status">${this.getStatusText(ticket.status)}</span>
                </div>
            `).join('');
        }
        
        // 更新操作特定的内容
        switch (operation) {
            case 'assign':
                this.updateAssignDialog(dialog, tickets);
                break;
            case 'approve':
                this.updateApproveDialog(dialog, tickets);
                break;
            case 'reject':
                this.updateRejectDialog(dialog, tickets);
                break;
            case 'delete':
                this.updateDeleteDialog(dialog, tickets);
                break;
        }
    },
    
    /**
     * 更新派单对话框
     * @param {Element} dialog 对话框元素
     * @param {Array} tickets 工单列表
     */
    updateAssignDialog: function(dialog, tickets) {
        const unitSelect = dialog.querySelector('#assignUnit');
        const handlerSelect = dialog.querySelector('#assignHandler');
        
        if (unitSelect) {
            // 加载承办单位列表
            this.loadUnits(unitSelect);
        }
        
        if (handlerSelect) {
            // 清空处理人列表
            handlerSelect.innerHTML = '<option value="">请先选择承办单位</option>';
        }
        
        // 绑定单位变更事件
        if (unitSelect && handlerSelect) {
            unitSelect.addEventListener('change', () => {
                this.loadHandlers(handlerSelect, unitSelect.value);
            });
        }
    },
    
    /**
     * 更新审核对话框
     * @param {Element} dialog 对话框元素
     * @param {Array} tickets 工单列表
     */
    updateApproveDialog: function(dialog, tickets) {
        const remarkTextarea = dialog.querySelector('#approveRemark');
        if (remarkTextarea) {
            remarkTextarea.value = '';
            remarkTextarea.placeholder = '请输入审核意见（可选）';
        }
    },
    
    /**
     * 更新退回对话框
     * @param {Element} dialog 对话框元素
     * @param {Array} tickets 工单列表
     */
    updateRejectDialog: function(dialog, tickets) {
        const reasonSelect = dialog.querySelector('#rejectReason');
        const remarkTextarea = dialog.querySelector('#rejectRemark');
        
        if (reasonSelect) {
            reasonSelect.innerHTML = `
                <option value="">请选择退回原因</option>
                <option value="incomplete">信息不完整</option>
                <option value="duplicate">重复工单</option>
                <option value="invalid">无效工单</option>
                <option value="other">其他原因</option>
            `;
        }
        
        if (remarkTextarea) {
            remarkTextarea.value = '';
            remarkTextarea.placeholder = '请详细说明退回原因';
        }
    },
    
    /**
     * 更新删除对话框
     * @param {Element} dialog 对话框元素
     * @param {Array} tickets 工单列表
     */
    updateDeleteDialog: function(dialog, tickets) {
        const warningText = dialog.querySelector('.delete-warning');
        if (warningText) {
            warningText.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                您即将删除 <strong>${tickets.length}</strong> 个工单，此操作不可恢复！
            `;
        }
    },
    
    /**
     * 执行操作
     * @param {string} operation 操作类型
     * @param {Array} tickets 工单列表
     * @param {Object} params 操作参数
     */
    executeOperation: function(operation, tickets, params = {}) {
        const ticketIds = tickets.map(ticket => ticket.id);
        
        switch (operation) {
            case 'assign':
                this.executeBatchAssign(ticketIds, params);
                break;
            case 'approve':
                this.executeBatchApprove(ticketIds, params);
                break;
            case 'reject':
                this.executeBatchReject(ticketIds, params);
                break;
            case 'export':
                this.executeBatchExport(tickets, params);
                break;
            case 'print':
                this.executeBatchPrint(tickets, params);
                break;
            case 'delete':
                this.executeBatchDelete(ticketIds, params);
                break;
            default:
                Utils.showMessage('未实现的操作', 'warning');
        }
    },
    
    /**
     * 执行批量派单
     * @param {Array} ticketIds 工单ID列表
     * @param {Object} params 派单参数
     */
    executeBatchAssign: function(ticketIds, params) {
        if (!params.unit) {
            Utils.showMessage('请选择承办单位', 'warning');
            return;
        }
        
        Utils.showMessage('正在执行批量派单...', 'info');
        
        // 模拟API调用
        setTimeout(() => {
            Utils.showMessage(`成功派发 ${ticketIds.length} 个工单`, 'success');
            this.closeDialog();
            this.refreshTable();
            this.clearSelection();
        }, 1500);
    },
    
    /**
     * 执行批量审核
     * @param {Array} ticketIds 工单ID列表
     * @param {Object} params 审核参数
     */
    executeBatchApprove: function(ticketIds, params) {
        Utils.showMessage('正在执行批量审核...', 'info');
        
        // 模拟API调用
        setTimeout(() => {
            Utils.showMessage(`成功审核 ${ticketIds.length} 个工单`, 'success');
            this.closeDialog();
            this.refreshTable();
            this.clearSelection();
        }, 1500);
    },
    
    /**
     * 执行批量退回
     * @param {Array} ticketIds 工单ID列表
     * @param {Object} params 退回参数
     */
    executeBatchReject: function(ticketIds, params) {
        if (!params.reason) {
            Utils.showMessage('请选择退回原因', 'warning');
            return;
        }
        
        Utils.showMessage('正在执行批量退回...', 'info');
        
        // 模拟API调用
        setTimeout(() => {
            Utils.showMessage(`成功退回 ${ticketIds.length} 个工单`, 'success');
            this.closeDialog();
            this.refreshTable();
            this.clearSelection();
        }, 1500);
    },
    
    /**
     * 执行批量导出
     * @param {Array} tickets 工单列表
     * @param {Object} params 导出参数
     */
    executeBatchExport: function(tickets, params) {
        Utils.showMessage('正在准备导出数据...', 'info');
        
        // 准备导出数据
        const exportData = tickets.map(ticket => ({
            '工单编号': ticket.ticketNo,
            '工单状态': this.getStatusText(ticket.status),
            '紧急程度': this.getUrgencyText(ticket.urgency),
            '处理模式': this.getModeText(ticket.mode),
            '市民姓名': ticket.citizen.name,
            '联系电话': ticket.citizen.phone,
            '工单标题': ticket.content.title,
            '工单分类': ticket.content.category,
            '地址': ticket.content.address,
            '创建时间': Utils.formatDateTime(ticket.createTime),
            '更新时间': Utils.formatDateTime(ticket.updateTime)
        }));
        
        // 导出CSV
        const filename = `工单数据_${Utils.formatDateTime(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`;
        Utils.exportToCSV(exportData, filename);
        
        Utils.showMessage('导出成功', 'success');
    },
    
    /**
     * 执行批量打印
     * @param {Array} tickets 工单列表
     * @param {Object} params 打印参数
     */
    executeBatchPrint: function(tickets, params) {
        Utils.showMessage('正在准备打印...', 'info');
        
        // 创建打印窗口
        const printWindow = window.open('', '_blank');
        const printContent = this.generatePrintContent(tickets);
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // 延迟打印，确保内容加载完成
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
            Utils.showMessage('打印任务已发送', 'success');
        }, 500);
    },
    
    /**
     * 执行批量删除
     * @param {Array} ticketIds 工单ID列表
     * @param {Object} params 删除参数
     */
    executeBatchDelete: function(ticketIds, params) {
        Utils.showMessage('正在执行批量删除...', 'info');
        
        // 模拟API调用
        setTimeout(() => {
            Utils.showMessage(`成功删除 ${ticketIds.length} 个工单`, 'success');
            this.closeDialog();
            this.refreshTable();
            this.clearSelection();
        }, 1500);
    },
    
    /**
     * 生成打印内容
     * @param {Array} tickets 工单列表
     * @returns {string} HTML内容
     */
    generatePrintContent: function(tickets) {
        const title = `工单列表 - ${Utils.formatDateTime(new Date())}`;
        
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>${title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { text-align: center; color: #333; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f5f5f5; font-weight: bold; }
                    .status { padding: 2px 6px; border-radius: 3px; font-size: 12px; }
                    .status.pending { background: #e6f7ff; color: #1890ff; }
                    .status.processing { background: #f6ffed; color: #52c41a; }
                    .status.closed { background: #f5f5f5; color: #666; }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <h1>${title}</h1>
                <p>共 ${tickets.length} 个工单</p>
                <table>
                    <thead>
                        <tr>
                            <th>工单编号</th>
                            <th>状态</th>
                            <th>紧急程度</th>
                            <th>市民姓名</th>
                            <th>联系电话</th>
                            <th>工单标题</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        tickets.forEach(ticket => {
            content += `
                <tr>
                    <td>${ticket.ticketNo}</td>
                    <td><span class="status ${ticket.status}">${this.getStatusText(ticket.status)}</span></td>
                    <td>${this.getUrgencyText(ticket.urgency)}</td>
                    <td>${ticket.citizen.name}</td>
                    <td>${ticket.citizen.phone}</td>
                    <td>${ticket.content.title}</td>
                    <td>${Utils.formatDateTime(ticket.createTime)}</td>
                </tr>
            `;
        });
        
        content += `
                    </tbody>
                </table>
            </body>
            </html>
        `;
        
        return content;
    },
    
    /**
     * 加载承办单位列表
     * @param {Element} select 选择框元素
     */
    loadUnits: function(select) {
        // 模拟单位数据
        const units = [
            { id: 'unit1', name: '市政管理局' },
            { id: 'unit2', name: '环保局' },
            { id: 'unit3', name: '交通运输局' },
            { id: 'unit4', name: '住建局' },
            { id: 'unit5', name: '城管执法局' }
        ];
        
        select.innerHTML = '<option value="">请选择承办单位</option>' +
            units.map(unit => `<option value="${unit.id}">${unit.name}</option>`).join('');
    },
    
    /**
     * 加载处理人列表
     * @param {Element} select 选择框元素
     * @param {string} unitId 单位ID
     */
    loadHandlers: function(select, unitId) {
        if (!unitId) {
            select.innerHTML = '<option value="">请先选择承办单位</option>';
            return;
        }
        
        // 模拟处理人数据
        const handlers = [
            { id: 'handler1', name: '张三' },
            { id: 'handler2', name: '李四' },
            { id: 'handler3', name: '王五' }
        ];
        
        select.innerHTML = '<option value="">请选择处理人（可选）</option>' +
            handlers.map(handler => `<option value="${handler.id}">${handler.name}</option>`).join('');
    },
    
    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText: function(status) {
        const statusMap = {
            draft: '草稿',
            pending: '待接收',
            processing: '处理中',
            reviewing: '待审核',
            callback: '待回访',
            closed: '已关闭'
        };
        return statusMap[status] || status;
    },
    
    /**
     * 获取紧急程度文本
     * @param {string} urgency 紧急程度
     * @returns {string} 紧急程度文本
     */
    getUrgencyText: function(urgency) {
        const urgencyMap = {
            normal: '一般',
            urgent: '紧急',
            critical: '特急'
        };
        return urgencyMap[urgency] || urgency;
    },
    
    /**
     * 获取处理模式文本
     * @param {string} mode 处理模式
     * @returns {string} 处理模式文本
     */
    getModeText: function(mode) {
        const modeMap = {
            instant: '即时办结',
            normal: '普通流转',
            cooperation: '多部门协同'
        };
        return modeMap[mode] || mode;
    },
    
    /**
     * 关闭对话框
     */
    closeDialog: function() {
        document.querySelectorAll('.dialog').forEach(dialog => {
            dialog.classList.remove('active');
        });

        const overlay = document.getElementById('overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    },
    
    /**
     * 刷新表格
     */
    refreshTable: function() {
        if (window.FilterManager) {
            window.FilterManager.applyFilters();
        }
    },
    
    /**
     * 清空选择
     */
    clearSelection: function() {
        if (window.TableManager) {
            window.TableManager.clearSelection();
        }
    },
    
    /**
     * 创建对话框
     */
    createDialogs: function() {
        // 这里可以动态创建对话框HTML
        // 实际项目中对话框通常在HTML中预定义
    }
};
